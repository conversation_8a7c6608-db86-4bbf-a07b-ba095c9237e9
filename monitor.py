#!/usr/bin/env python3
"""
Eko Backend Multi-Instance Monitor
Monitors health, load distribution, and sticky session behavior
"""

import asyncio
import aiohttp
import json
import time
import subprocess
import sys
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict, Counter

class EkoMonitor:
    def __init__(self, base_url: str = "http://localhost:8201"):
        self.base_url = base_url
        self.session = None
        self.request_stats = defaultdict(int)
        self.instance_responses = Counter()
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_running_instances(self) -> List[str]:
        """Get list of running eko-api containers."""
        try:
            result = subprocess.run(
                ["docker-compose", "ps", "-q", "eko-api"],
                capture_output=True, text=True, check=True
            )
            container_ids = result.stdout.strip().split('\n')
            return [cid for cid in container_ids if cid]
        except subprocess.CalledProcessError:
            return []
    
    def get_container_info(self, container_id: str) -> Dict[str, Any]:
        """Get detailed info about a container."""
        try:
            result = subprocess.run(
                ["docker", "inspect", container_id],
                capture_output=True, text=True, check=True
            )
            info = json.loads(result.stdout)[0]
            return {
                "id": container_id[:12],
                "name": info["Name"].lstrip("/"),
                "status": info["State"]["Status"],
                "started": info["State"]["StartedAt"],
                "health": info["State"].get("Health", {}).get("Status", "unknown")
            }
        except (subprocess.CalledProcessError, json.JSONDecodeError, KeyError):
            return {"id": container_id[:12], "status": "unknown"}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the load balancer."""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    # Try to identify which instance responded
                    server_header = response.headers.get('Server', '')
                    return {
                        "status": "healthy",
                        "response_time": response.headers.get('X-Response-Time', 'unknown'),
                        "server": server_header,
                        "data": data
                    }
                else:
                    return {"status": "unhealthy", "code": response.status}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    async def test_load_distribution(self, num_requests: int = 10) -> Dict[str, Any]:
        """Test load distribution across instances."""
        print(f"🔄 Testing load distribution with {num_requests} requests...")
        
        responses = []
        cookies = {}
        
        for i in range(num_requests):
            try:
                async with self.session.get(
                    f"{self.base_url}/health",
                    cookies=cookies
                ) as response:
                    # Update cookies for sticky session testing
                    if response.cookies:
                        cookies.update({k: v.value for k, v in response.cookies.items()})
                    
                    responses.append({
                        "request": i + 1,
                        "status": response.status,
                        "cookies": dict(response.cookies),
                        "headers": dict(response.headers)
                    })
                    
                await asyncio.sleep(0.1)  # Small delay between requests
                
            except Exception as e:
                responses.append({
                    "request": i + 1,
                    "status": "error",
                    "error": str(e)
                })
        
        # Analyze responses
        successful = len([r for r in responses if r.get("status") == 200])
        has_sticky_cookie = any("eko-session" in r.get("cookies", {}) for r in responses)
        
        return {
            "total_requests": num_requests,
            "successful": successful,
            "success_rate": f"{(successful/num_requests)*100:.1f}%",
            "sticky_sessions": "enabled" if has_sticky_cookie else "disabled",
            "responses": responses
        }
    
    async def monitor_websocket_endpoint(self) -> Dict[str, Any]:
        """Test WebSocket endpoint availability."""
        try:
            ws_url = f"ws://localhost:8201/setup_files?token=test"
            async with self.session.ws_connect(ws_url) as ws:
                return {"status": "available", "message": "WebSocket endpoint reachable"}
        except aiohttp.ClientResponseError as e:
            if e.status in [401, 403]:
                return {"status": "available", "message": "WebSocket endpoint exists (auth required)"}
            else:
                return {"status": "error", "error": f"HTTP {e.status}"}
        except Exception as e:
            if "Missing token" in str(e) or "401" in str(e):
                return {"status": "available", "message": "WebSocket endpoint exists (auth required)"}
            else:
                return {"status": "error", "error": str(e)}
    
    def display_dashboard(self, instances: List[Dict], health: Dict, load_test: Dict, websocket: Dict):
        """Display monitoring dashboard."""
        # Clear screen
        print("\033[2J\033[H")
        
        # Header
        print("🚀 Eko Backend Multi-Instance Monitor")
        print("=" * 60)
        print(f"⏰ Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Instance Status
        print("📊 Instance Status:")
        print("-" * 40)
        if instances:
            for instance in instances:
                status_icon = "✅" if instance["status"] == "running" else "❌"
                print(f"  {status_icon} {instance['name']:<25} {instance['status']}")
        else:
            print("  ❌ No instances running")
        print()
        
        # Health Check
        print("🏥 Health Check:")
        print("-" * 40)
        if health["status"] == "healthy":
            print(f"  ✅ Status: {health['status']}")
            print(f"  📡 Service: {health['data'].get('service', 'unknown')}")
            print(f"  🔢 Version: {health['data'].get('version', 'unknown')}")
        else:
            print(f"  ❌ Status: {health['status']}")
            if "error" in health:
                print(f"  🚨 Error: {health['error']}")
        print()
        
        # Load Distribution
        print("⚖️  Load Distribution Test:")
        print("-" * 40)
        print(f"  📊 Total Requests: {load_test['total_requests']}")
        print(f"  ✅ Successful: {load_test['successful']}")
        print(f"  📈 Success Rate: {load_test['success_rate']}")
        print(f"  🍪 Sticky Sessions: {load_test['sticky_sessions']}")
        print()
        
        # WebSocket Status
        print("🔌 WebSocket Status:")
        print("-" * 40)
        ws_icon = "✅" if websocket["status"] == "available" else "❌"
        print(f"  {ws_icon} Status: {websocket['status']}")
        print(f"  💬 Message: {websocket.get('message', websocket.get('error', 'unknown'))}")
        print()
        
        # Access Points
        print("🔗 Access Points:")
        print("-" * 40)
        print("  📚 API Docs: http://localhost:8201/docs")
        print("  📊 Traefik Dashboard: http://localhost:8202")
        print("  🔍 API Info: http://localhost:8201/api-info")
        print("  💓 Health Check: http://localhost:8201/health")
        print()
        
        # Instructions
        print("⌨️  Commands:")
        print("-" * 40)
        print("  • Press Ctrl+C to stop monitoring")
        print("  • Use './scale.sh scale <N>' to change instance count")
        print("  • Use './scale.sh status' for detailed status")

async def main():
    """Main monitoring loop."""
    print("🚀 Starting Eko Backend Multi-Instance Monitor...")
    
    async with EkoMonitor() as monitor:
        try:
            while True:
                # Gather data
                instances = []
                container_ids = monitor.get_running_instances()
                
                for container_id in container_ids:
                    instance_info = monitor.get_container_info(container_id)
                    instances.append(instance_info)
                
                health = await monitor.health_check()
                load_test = await monitor.test_load_distribution(5)
                websocket = await monitor.monitor_websocket_endpoint()
                
                # Display dashboard
                monitor.display_dashboard(instances, health, load_test, websocket)
                
                # Wait before next update
                await asyncio.sleep(10)
                
        except KeyboardInterrupt:
            print("\n\n👋 Monitoring stopped by user")
        except Exception as e:
            print(f"\n\n❌ Error: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
