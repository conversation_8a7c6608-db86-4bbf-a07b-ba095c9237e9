#!/usr/bin/env python3
"""
Test script to verify the Eko Backend setup with Traefik configuration.
Tests the document processing flow and WebSocket connectivity.
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

class EkoSetupTester:
    def __init__(self, base_url: str = "http://localhost:8201"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self) -> Dict[str, Any]:
        """Test basic health check endpoint."""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    return {"status": "✅ PASS", "data": data}
                else:
                    return {"status": "❌ FAIL", "error": f"HTTP {response.status}"}
        except Exception as e:
            return {"status": "❌ FAIL", "error": str(e)}
    
    async def test_api_info(self) -> Dict[str, Any]:
        """Test API info endpoint."""
        try:
            async with self.session.get(f"{self.base_url}/api-info") as response:
                if response.status == 200:
                    data = await response.json()
                    return {"status": "✅ PASS", "data": data}
                else:
                    return {"status": "❌ FAIL", "error": f"HTTP {response.status}"}
        except Exception as e:
            return {"status": "❌ FAIL", "error": str(e)}
    
    async def test_sticky_session_cookie(self) -> Dict[str, Any]:
        """Test if sticky session cookie is set."""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                cookies = response.cookies
                if 'eko-session' in cookies:
                    return {"status": "✅ PASS", "cookie": str(cookies['eko-session'])}
                else:
                    return {"status": "⚠️  WARNING", "message": "No sticky session cookie found"}
        except Exception as e:
            return {"status": "❌ FAIL", "error": str(e)}
    
    async def test_websocket_endpoint(self) -> Dict[str, Any]:
        """Test WebSocket endpoint availability (connection test only)."""
        try:
            # Test if the WebSocket endpoint is reachable
            ws_url = f"ws://localhost:8201/setup_files?token=test"
            async with self.session.ws_connect(ws_url) as ws:
                return {"status": "✅ PASS", "message": "WebSocket endpoint reachable"}
        except aiohttp.ClientResponseError as e:
            if e.status == 401 or e.status == 403:
                return {"status": "✅ PASS", "message": "WebSocket endpoint exists (auth required)"}
            else:
                return {"status": "❌ FAIL", "error": f"HTTP {e.status}"}
        except Exception as e:
            if "Missing token" in str(e) or "401" in str(e):
                return {"status": "✅ PASS", "message": "WebSocket endpoint exists (auth required)"}
            else:
                return {"status": "❌ FAIL", "error": str(e)}
    
    async def test_process_documents_endpoint(self) -> Dict[str, Any]:
        """Test process documents endpoint availability."""
        try:
            # Test POST to process-documents (should fail due to auth, but endpoint should exist)
            async with self.session.post(f"{self.base_url}/process-documents") as response:
                if response.status in [401, 403, 422]:  # Auth or validation error is expected
                    return {"status": "✅ PASS", "message": "Endpoint exists (auth/validation required)"}
                else:
                    return {"status": "❌ FAIL", "error": f"Unexpected status: {response.status}"}
        except Exception as e:
            return {"status": "❌ FAIL", "error": str(e)}

async def test_traefik_dashboard():
    """Test Traefik dashboard on port 8202."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8202/api/overview") as response:
                if response.status == 200:
                    return {"status": "✅ PASS", "message": "Traefik dashboard accessible"}
                else:
                    return {"status": "❌ FAIL", "error": f"HTTP {response.status}"}
    except Exception as e:
        return {"status": "❌ FAIL", "error": str(e)}

async def main():
    """Run all tests and display results."""
    print("🚀 Eko Backend Setup Test Suite")
    print("=" * 50)
    
    # Test API endpoints on port 8201
    async with EkoSetupTester() as tester:
        tests = [
            ("Health Check", tester.test_health_check()),
            ("API Info", tester.test_api_info()),
            ("Sticky Session Cookie", tester.test_sticky_session_cookie()),
            ("WebSocket Endpoint", tester.test_websocket_endpoint()),
            ("Process Documents Endpoint", tester.test_process_documents_endpoint()),
        ]
        
        for test_name, test_coro in tests:
            print(f"\n📋 Testing: {test_name}")
            result = await test_coro
            print(f"   Result: {result['status']}")
            if 'data' in result:
                print(f"   Data: {json.dumps(result['data'], indent=2)}")
            elif 'message' in result:
                print(f"   Message: {result['message']}")
            elif 'error' in result:
                print(f"   Error: {result['error']}")
    
    # Test Traefik dashboard on port 8202
    print(f"\n📋 Testing: Traefik Dashboard")
    traefik_result = await test_traefik_dashboard()
    print(f"   Result: {traefik_result['status']}")
    if 'message' in traefik_result:
        print(f"   Message: {traefik_result['message']}")
    elif 'error' in traefik_result:
        print(f"   Error: {traefik_result['error']}")
    
    print("\n" + "=" * 50)
    print("🏁 Test Suite Complete")
    print("\n📝 Next Steps:")
    print("   1. Start services: docker-compose up -d")
    print("   2. Check logs: docker-compose logs -f")
    print("   3. Access API docs: http://localhost:8201/docs")
    print("   4. Access Traefik dashboard: http://localhost:8202")

if __name__ == "__main__":
    asyncio.run(main())
